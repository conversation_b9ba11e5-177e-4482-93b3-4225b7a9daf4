// Simple test script to verify custom time range functionality
// Run with: node test-custom-time-range.js

const BASE_URL = 'http://localhost:3001';

async function testCustomTimeRangeAPI() {
  console.log('🧪 Testing Custom Time Range API...\n');

  // Test 1: Valid custom time range
  console.log('Test 1: Valid custom time range');
  try {
    const response = await fetch(`${BASE_URL}/api/availability/custom-range?date=2025-07-02&startTime=10:00&endTime=11:30`);
    const result = await response.json();
    console.log('✅ Response:', result);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Invalid time range (end before start)
  console.log('Test 2: Invalid time range (end before start)');
  try {
    const response = await fetch(`${BASE_URL}/api/availability/custom-range?date=2025-07-02&startTime=11:30&endTime=10:00`);
    const result = await response.json();
    console.log('✅ Response:', result);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Outside business hours
  console.log('Test 3: Outside business hours');
  try {
    const response = await fetch(`${BASE_URL}/api/availability/custom-range?date=2025-07-02&startTime=08:00&endTime=09:00`);
    const result = await response.json();
    console.log('✅ Response:', result);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Weekend date
  console.log('Test 4: Weekend date');
  try {
    const response = await fetch(`${BASE_URL}/api/availability/custom-range?date=2025-12-28&startTime=10:00&endTime=11:00`);
    const result = await response.json();
    console.log('✅ Response:', result);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 5: Missing parameters
  console.log('Test 5: Missing parameters');
  try {
    const response = await fetch(`${BASE_URL}/api/availability/custom-range?date=2025-07-02&startTime=10:00`);
    const result = await response.json();
    console.log('✅ Response:', result);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

// Test the regular availability API to ensure it still works
async function testRegularAvailabilityAPI() {
  console.log('\n🧪 Testing Regular Availability API...\n');

  try {
    const response = await fetch(`${BASE_URL}/api/availability?date=2025-07-02`);
    const result = await response.json();
    console.log('✅ Regular availability API response:', {
      success: result.success,
      availableSlots: result.availableSlots?.length || 0,
      message: result.message
    });
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Custom Time Range Tests\n');
  console.log('='.repeat(60) + '\n');

  await testCustomTimeRangeAPI();
  await testRegularAvailabilityAPI();

  console.log('\n' + '='.repeat(60));
  console.log('✅ Tests completed!');
}

runTests().catch(console.error);
