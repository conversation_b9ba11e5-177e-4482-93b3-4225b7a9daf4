'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { services, servicePrestazioni, isWeekday, getMinDate, isValidPhone, isValidEmail } from '../lib/utils';

export default function AdminBookingForm({ token, onAppointmentCreated }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [availabilityData, setAvailabilityData] = useState(null);
  const [isLoadingAvailability, setIsLoadingAvailability] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [availabilityError, setAvailabilityError] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [availablePrestazioni, setAvailablePrestazioni] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);
  const [useCustomTimeRange, setUseCustomTimeRange] = useState(false);
  const [customStartTime, setCustomStartTime] = useState('');
  const [customEndTime, setCustomEndTime] = useState('');
  const [customTimeValidation, setCustomTimeValidation] = useState({ isValid: true, error: null });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm();

  // Watch for changes in service and date
  const watchedService = watch('servizio');
  const watchedDate = watch('dataAppuntamento');

  // Custom time range validation functions
  const validateTimeRange = (startTime, endTime) => {
    if (!startTime || !endTime) return { isValid: false, error: 'Entrambi gli orari sono obbligatori' };

    const start = new Date(`2000-01-01T${startTime}:00`);
    const end = new Date(`2000-01-01T${endTime}:00`);

    if (end <= start) {
      return { isValid: false, error: 'L\'orario di fine deve essere successivo all\'orario di inizio' };
    }

    // Check business hours (9 AM - 6 PM)
    const businessStart = new Date(`2000-01-01T09:00:00`);
    const businessEnd = new Date(`2000-01-01T18:00:00`);

    if (start < businessStart || end > businessEnd) {
      return { isValid: false, error: 'Gli orari devono essere compresi tra le 09:00 e le 18:00' };
    }

    return { isValid: true, error: null };
  };

  const formatTimeRange = (startTime, endTime) => {
    return `${startTime}-${endTime}`;
  };

  // Check if custom time range conflicts with existing appointments
  const checkCustomTimeRangeAvailability = async (date, startTime, endTime) => {
    if (!date || !startTime || !endTime) return { isAvailable: false, error: 'Parametri mancanti' };

    try {
      const response = await fetch(`/api/availability/custom-range?date=${date}&startTime=${startTime}&endTime=${endTime}`);
      const result = await response.json();

      if (response.ok && result.success) {
        return { isAvailable: result.isAvailable, error: result.message || null };
      } else {
        return { isAvailable: false, error: result.message || 'Errore nel controllo disponibilità' };
      }
    } catch (error) {
      console.error('Error checking custom time range availability:', error);
      return { isAvailable: false, error: 'Errore di connessione' };
    }
  };

  // Fetch available employees for selected date and time
  const fetchAvailableEmployees = async (date = null, time = null) => {
    try {
      setIsLoadingEmployees(true);

      let url = '/api/employees';
      if (date) {
        url = `/api/employees/available?date=${date}`;
        if (time) {
          url += `&time=${time}`;
        }
      }

      const response = await fetch(url);
      const result = await response.json();

      if (response.ok && result.success) {
        if (date) {
          // Available employees for specific date/time
          setEmployees(result.availableEmployees || []);
        } else {
          // All active employees
          setEmployees(result.data || []);
        }
      } else {
        console.error('Error fetching employees:', result.message);
        // Fallback to default employees if API fails
        setEmployees([
          { id: 'qualsiasi', name: 'Qualsiasi' },
          { id: 'antonello', name: 'Antonello' },
          { id: 'federica', name: 'Federica' },
          { id: 'giuseppe', name: 'Giuseppe' },
          { id: 'silvia', name: 'Silvia' },
          { id: 'tania', name: 'Tania' }
        ]);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
      // Fallback to default employees if API fails
      setEmployees([
        { id: 'qualsiasi', name: 'Qualsiasi' },
        { id: 'antonello', name: 'Antonello' },
        { id: 'federica', name: 'Federica' },
        { id: 'giuseppe', name: 'Giuseppe' },
        { id: 'silvia', name: 'Silvia' },
        { id: 'tania', name: 'Tania' }
      ]);
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Load employees on component mount
  useEffect(() => {
    fetchAvailableEmployees();
  }, []);

  // Update available employees when date/time changes
  useEffect(() => {
    const selectedDate = watch('dataAppuntamento');
    const selectedTime = watch('orario');

    if (selectedDate && selectedTime) {
      fetchAvailableEmployees(selectedDate, selectedTime);
    } else if (selectedDate) {
      fetchAvailableEmployees(selectedDate);
    }
  }, [watchedDate, watch('orario')]);

  // Validate custom time range in real-time
  useEffect(() => {
    if (useCustomTimeRange && customStartTime && customEndTime && watchedDate) {
      const validation = validateTimeRange(customStartTime, customEndTime);
      setCustomTimeValidation(validation);

      // If basic validation passes, check availability
      if (validation.isValid) {
        checkCustomTimeRangeAvailability(watchedDate, customStartTime, customEndTime)
          .then(availabilityResult => {
            if (!availabilityResult.isAvailable) {
              setCustomTimeValidation({
                isValid: false,
                error: availabilityResult.error || 'Orario non disponibile'
              });
            }
          })
          .catch(error => {
            console.error('Error checking custom time availability:', error);
          });
      }
    } else if (useCustomTimeRange) {
      setCustomTimeValidation({ isValid: true, error: null });
    }
  }, [useCustomTimeRange, customStartTime, customEndTime, watchedDate]);

  // Update available prestazioni when service changes
  useEffect(() => {
    if (watchedService && watchedService !== selectedService) {
      setSelectedService(watchedService);
      setAvailablePrestazioni(servicePrestazioni[watchedService] || []);
      // Reset prestazione when service changes
      setValue('prestazione', '');
    }
  }, [watchedService, selectedService, setValue]);

  // Fetch availability when date changes
  useEffect(() => {
    if (watchedDate && watchedDate !== selectedDate) {
      setSelectedDate(watchedDate);
      if (isWeekday(watchedDate)) {
        fetchAvailability(watchedDate);
      } else {
        setAvailabilityData(null);
        setAvailabilityError('Gli appuntamenti sono disponibili solo dal lunedì al venerdì');
      }
    }
  }, [watchedDate, selectedDate]);

  const fetchAvailability = async (date) => {
    if (!date) return;

    setIsLoadingAvailability(true);
    setAvailabilityError('');

    try {
      const response = await fetch(`/api/availability?date=${date}`);
      const result = await response.json();

      if (result.success) {
        setAvailabilityData(result);

        // Clear the time selection if the currently selected time is no longer available
        const currentTime = watch('orario');
        if (currentTime && !result.availableSlots.includes(currentTime)) {
          setValue('orario', '');
        }
      } else {
        setAvailabilityError(result.message || 'Errore nel controllo disponibilità');
        setAvailabilityData(null);
      }
    } catch (error) {
      setAvailabilityError('Errore di connessione. Riprova più tardi.');
      setAvailabilityData(null);
    } finally {
      setIsLoadingAvailability(false);
    }
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitMessage('');

    // Handle custom time range vs predefined slots
    let finalData = { ...data };

    if (useCustomTimeRange) {
      // Validate custom time range
      const validation = validateTimeRange(customStartTime, customEndTime);
      if (!validation.isValid) {
        setSubmitMessage(validation.error);
        setIsSubmitting(false);
        return;
      }

      // Check availability for custom time range
      const availabilityCheck = await checkCustomTimeRangeAvailability(
        finalData.dataAppuntamento,
        customStartTime,
        customEndTime
      );

      if (!availabilityCheck.isAvailable) {
        setSubmitMessage(availabilityCheck.error || 'L\'orario personalizzato selezionato non è disponibile.');
        setIsSubmitting(false);
        return;
      }

      // Use custom time range
      finalData.orario = customStartTime; // Use start time for orario field (TIME type compatibility)
      finalData.customTimeRange = true;
      finalData.startTime = customStartTime;
      finalData.endTime = customEndTime;
    } else {
      // Final availability check for predefined slots
      if (availabilityData && !availabilityData.availableSlots.includes(data.orario)) {
        setSubmitMessage('L\'orario selezionato non è più disponibile. Seleziona un altro orario.');
        setIsSubmitting(false);
        return;
      }
      finalData.customTimeRange = false;
    }

    // Handle empty name and email fields by substituting "Non registrato"
    const processedData = {
      ...finalData,
      nome: finalData.nome?.trim() || 'Non registrato',
      email: finalData.email?.trim() || 'Non registrato'
    };

    try {
      const response = await fetch('/api/admin/appointments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(processedData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitMessage('Appuntamento creato con successo!');
        reset();
        setSelectedDate('');
        setAvailabilityData(null);
        setSelectedService('');
        setAvailablePrestazioni([]);
        setUseCustomTimeRange(false);
        setCustomStartTime('');
        setCustomEndTime('');

        // Notify parent component
        if (onAppointmentCreated) {
          onAppointmentCreated(result.appointment);
        }
      } else {
        if (result.code === 'TIME_SLOT_UNAVAILABLE') {
          setSubmitMessage('L\'orario selezionato non è più disponibile. Ricarica la disponibilità e scegli un altro orario.');
          // Refresh availability for the selected date
          if (data.dataAppuntamento) {
            fetchAvailability(data.dataAppuntamento);
          }
        } else {
          setSubmitMessage(result.message || 'Errore nella creazione dell\'appuntamento. Riprova più tardi.');
        }
      }
    } catch (error) {
      setSubmitMessage('Errore di connessione. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-[var(--primary-text)] mb-2">
          Crea Nuovo Appuntamento
        </h2>
        <p className="text-[var(--secondary-text)]">
          Compila il modulo per creare un appuntamento per conto di un cliente
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Nome */}
        <div>
          <label htmlFor="nome" className="form-label">
            Nome (opzionale)
          </label>
          <input
            type="text"
            id="nome"
            className="form-input"
            placeholder="Lascia vuoto per 'Non registrato'"
            {...register('nome', {
              minLength: { value: 2, message: 'Il nome deve avere almeno 2 caratteri' }
            })}
          />
          {errors.nome && (
            <p className="error-message">{errors.nome.message}</p>
          )}
        </div>

        {/* Cognome */}
        <div>
          <label htmlFor="cognome" className="form-label">
            Cognome *
          </label>
          <input
            type="text"
            id="cognome"
            className="form-input"
            {...register('cognome', { 
              required: 'Il cognome è obbligatorio',
              minLength: { value: 2, message: 'Il cognome deve avere almeno 2 caratteri' }
            })}
          />
          {errors.cognome && (
            <p className="error-message">{errors.cognome.message}</p>
          )}
        </div>

        {/* Telefono */}
        <div>
          <label htmlFor="telefono" className="form-label">
            Telefono *
          </label>
          <input
            type="tel"
            id="telefono"
            className="form-input"
            {...register('telefono', {
              required: 'Il numero di telefono è obbligatorio',
              validate: {
                isValid: (value) =>
                  isValidPhone(value) || 'Inserisci un numero di telefono valido'
              }
            })}
          />
          {errors.telefono && (
            <p className="error-message">{errors.telefono.message}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label htmlFor="email" className="form-label">
            Email (opzionale)
          </label>
          <input
            type="email"
            id="email"
            className="form-input"
            placeholder="Lascia vuoto per 'Non registrato'"
            {...register('email', {
              validate: {
                isValid: (value) =>
                  !value || value.trim() === '' || isValidEmail(value) || 'Inserisci un indirizzo email valido'
              }
            })}
          />
          {errors.email && (
            <p className="error-message">{errors.email.message}</p>
          )}
        </div>

        {/* Servizio */}
        <div>
          <label htmlFor="servizio" className="form-label">
            Servizio *
          </label>
          <select
            id="servizio"
            className="form-input"
            {...register('servizio', {
              required: 'Seleziona un servizio'
            })}
          >
            <option value="">Seleziona un servizio</option>
            {services.map((service) => (
              <option key={service} value={service}>
                {service}
              </option>
            ))}
          </select>
          {errors.servizio && (
            <p className="error-message">{errors.servizio.message}</p>
          )}
        </div>

        {/* Prestazione (conditional) */}
        {availablePrestazioni.length > 0 && (
          <div>
            <label htmlFor="prestazione" className="form-label">
              Prestazione
            </label>
            <select
              id="prestazione"
              className="form-input"
              {...register('prestazione')}
            >
              <option value="">Seleziona una prestazione (opzionale)</option>
              {availablePrestazioni.map((prestazione) => (
                <option key={prestazione} value={prestazione}>
                  {prestazione}
                </option>
              ))}
            </select>
            {errors.prestazione && (
              <p className="error-message">{errors.prestazione.message}</p>
            )}
          </div>
        )}

        {/* Operatore */}
        <div>
          <label htmlFor="operatore" className="form-label">
            Operatore
          </label>
          <select
            id="operatore"
            className="form-input"
            {...register('operatore')}
            defaultValue="Qualsiasi"
            disabled={isLoadingEmployees}
          >
            {isLoadingEmployees ? (
              <option value="">Caricamento operatori...</option>
            ) : employees.length === 0 ? (
              <option value="">Nessun operatore disponibile per l'orario selezionato</option>
            ) : (
              employees.map((employee) => (
                <option key={employee.employee_id || employee.id} value={employee.employee_name || employee.name}>
                  {employee.employee_name || employee.name}
                  {employee.role && employee.role !== 'Generale' && ` (${employee.role})`}
                </option>
              ))
            )}
          </select>
          {errors.operatore && (
            <p className="error-message">{errors.operatore.message}</p>
          )}
          {watchedDate && availabilityData && employees.length === 0 && !isLoadingEmployees && (
            <p className="text-sm text-amber-600 mt-1">
              Nessun operatore disponibile per l'orario selezionato. Prova un altro orario.
            </p>
          )}
        </div>

        {/* Note Aggiuntive */}
        <div>
          <label htmlFor="noteAggiuntive" className="form-label">
            Note Aggiuntive (opzionale)
          </label>
          <textarea
            id="noteAggiuntive"
            className="form-input"
            rows="3"
            placeholder="Inserisci eventuali note aggiuntive..."
            {...register('noteAggiuntive')}
          />
          {errors.noteAggiuntive && (
            <p className="error-message">{errors.noteAggiuntive.message}</p>
          )}
        </div>

        {/* Data Appuntamento */}
        <div>
          <label htmlFor="dataAppuntamento" className="form-label">
            Data Appuntamento *
          </label>
          <input
            type="date"
            id="dataAppuntamento"
            className="form-input"
            min={getMinDate()}
            {...register('dataAppuntamento', {
              required: 'La data dell\'appuntamento è obbligatoria',
              validate: {
                isWeekday: (value) =>
                  isWeekday(value) || 'Seleziona un giorno dal lunedì al venerdì'
              }
            })}
          />

          {/* Date selection helper */}
          <div className="mt-2 text-sm text-[var(--secondary-text)]">
            <p>💡 Gli appuntamenti sono disponibili dal lunedì al venerdì</p>
            {watchedDate && !isLoadingAvailability && availabilityData && (
              <p className="mt-1">
                {availabilityData.success ? (
                  availabilityData.summary.available > 0 ? (
                    <span className="text-green-600">
                      ✅ {availabilityData.summary.available} orari disponibili per {watchedDate}
                    </span>
                  ) : (
                    <span className="text-red-600">
                      ❌ Nessun orario disponibile per {selectedDate}
                    </span>
                  )
                ) : (
                  <span className="text-red-600">
                    ❌ {availabilityData.message}
                  </span>
                )}
              </p>
            )}
          </div>

          {errors.dataAppuntamento && (
            <p className="error-message">{errors.dataAppuntamento.message}</p>
          )}
        </div>

        {/* Orario */}
        <div>
          <label className="form-label">
            Orario *
          </label>

          {/* Time Selection Mode Toggle */}
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="timeMode"
                  checked={!useCustomTimeRange}
                  onChange={() => {
                    setUseCustomTimeRange(false);
                    setCustomStartTime('');
                    setCustomEndTime('');
                  }}
                  className="mr-2"
                />
                <span className="text-sm font-medium">Orari predefiniti</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="timeMode"
                  checked={useCustomTimeRange}
                  onChange={() => setUseCustomTimeRange(true)}
                  className="mr-2"
                />
                <span className="text-sm font-medium">Orario personalizzato</span>
              </label>
            </div>
            <p className="text-xs text-blue-600 mt-2">
              {useCustomTimeRange
                ? "Seleziona un orario di inizio e fine personalizzato per questo appuntamento"
                : "Scegli da uno degli orari predefiniti disponibili"
              }
            </p>
          </div>

          {/* Custom Time Range Selection */}
          {useCustomTimeRange ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="customStartTime" className="block text-sm font-medium text-gray-700 mb-1">
                    Orario di inizio *
                  </label>
                  <input
                    type="time"
                    id="customStartTime"
                    min="09:00"
                    max="17:30"
                    step="900" // 15 minute intervals
                    value={customStartTime}
                    onChange={(e) => setCustomStartTime(e.target.value)}
                    className="form-input"
                    required={useCustomTimeRange}
                  />
                </div>
                <div>
                  <label htmlFor="customEndTime" className="block text-sm font-medium text-gray-700 mb-1">
                    Orario di fine *
                  </label>
                  <input
                    type="time"
                    id="customEndTime"
                    min="09:15"
                    max="18:00"
                    step="900" // 15 minute intervals
                    value={customEndTime}
                    onChange={(e) => setCustomEndTime(e.target.value)}
                    className="form-input"
                    required={useCustomTimeRange}
                  />
                </div>
              </div>

              {/* Custom time validation feedback */}
              {customStartTime && customEndTime && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  {customTimeValidation.isValid ? (
                    <p className="text-sm text-green-600">
                      ✅ Durata appuntamento: {formatTimeRange(customStartTime, customEndTime)}
                    </p>
                  ) : (
                    <p className="text-sm text-red-600">
                      ❌ {customTimeValidation.error}
                    </p>
                  )}
                </div>
              )}
            </div>
          ) : (
            <>
              {/* Loading state */}
              {isLoadingAvailability && (
                <div className="flex items-center space-x-2 mb-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[var(--primary-red)]"></div>
                  <span className="text-sm text-[var(--secondary-text)]">Controllo disponibilità...</span>
                </div>
              )}

              {/* Availability error */}
              {availabilityError && (
                <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                  {availabilityError}
                </div>
              )}

              <select
                id="orario"
                className="form-input"
                disabled={!watchedDate || isLoadingAvailability || (availabilityData && availabilityData.summary.available === 0)}
                {...register('orario', {
                  required: useCustomTimeRange ? false : 'Seleziona un orario',
                  validate: {
                    isAvailable: (value) => {
                      if (useCustomTimeRange) return true; // Skip validation for custom time range
                      if (!value) return true; // Let required validation handle empty values
                      if (!availabilityData) return 'Seleziona prima una data';
                      return availabilityData.availableSlots.includes(value) || 'Questo orario non è più disponibile';
                    }
                  }
                })}
              >
                <option value="">
                  {!selectedDate
                    ? 'Seleziona prima una data'
                    : isLoadingAvailability
                      ? 'Controllo disponibilità...'
                      : availabilityData && availabilityData.summary.available === 0
                        ? 'Nessun orario disponibile'
                        : 'Seleziona un orario'
                  }
                </option>

                {availabilityData && availabilityData.availableSlots.map((time) => (
                  <option key={time} value={time}>
                    {time}
                  </option>
                ))}
              </select>

              {/* Show booked slots information */}
              {availabilityData && availabilityData.bookedSlots.length > 0 && (
                <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
                  <strong>Orari non disponibili:</strong> {availabilityData.bookedSlots.join(', ')}
                </div>
              )}

              {errors.orario && !useCustomTimeRange && (
                <p className="error-message">{errors.orario.message}</p>
              )}
            </>
          )}
        </div>

        {/* Admin Note */}
        <div className="text-sm text-[var(--secondary-text)] italic bg-blue-50 p-4 rounded-lg border border-blue-200">
          <strong>Nota per l'amministratore:</strong> Questo appuntamento verrà creato direttamente nel sistema.
          Se l'email è fornita, il cliente riceverà una email di conferma automatica.
          I campi nome ed email sono opzionali e verranno salvati come "Non registrato" se lasciati vuoti.
          <br /><br />
          <strong>Orario personalizzato:</strong> Come amministratore, puoi selezionare orari personalizzati
          che non sono limitati agli slot predefiniti. Questa funzionalità è disponibile solo nel pannello admin.
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? 'Creazione in corso...' : 'Crea Appuntamento'}
        </button>

        {/* Submit Message */}
        {submitMessage && (
          <div className={`p-4 rounded-lg text-sm ${
            submitMessage.includes('successo')
              ? 'bg-green-50 text-green-700 border border-green-200'
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {submitMessage}
          </div>
        )}
      </form>
    </div>
  );
}
