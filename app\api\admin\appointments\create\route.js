import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { authenticateAdmin } from '../../../../../lib/supabaseAuthUtils.js';
import { addAppointment, isTimeSlotAvailable } from '../../../../../lib/supabaseAppointmentUtils.js';
import { isWeekday, formatDate } from '../../../../../lib/utils.js';

// Configure your email settings here
const CAF_EMAIL = process.env.CAF_EMAIL || '<EMAIL>';
const EMAIL_USER = process.env.EMAIL_USER || '<EMAIL>';
const EMAIL_PASS = process.env.EMAIL_PASS || 'jpxjlwdlvfkceqgi';

export async function POST(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const data = await request.json();
    let { nome, cognome, telefono, email, servizio, prestazione, operatore, noteAggiuntive, dataAppuntamento, orario, customTimeRange, startTime, endTime } = data;

    // Handle empty name and email by substituting "Non registrato"
    nome = nome?.trim() || 'Non registrato';
    email = email?.trim() || 'Non registrato';

    // Update the data object with processed values
    const processedData = { ...data, nome, email };

    // Check if email is valid (not "Non registrato")
    const hasValidEmail = email !== 'Non registrato' && email.includes('@');

    // Validate required fields (nome and email are now optional)
    if (!cognome || !telefono || !servizio || !dataAppuntamento) {
      return NextResponse.json(
        { success: false, message: 'Cognome, telefono, servizio e data sono obbligatori' },
        { status: 400 }
      );
    }

    // Validate time fields based on whether custom time range is used
    if (customTimeRange) {
      if (!startTime || !endTime) {
        return NextResponse.json(
          { success: false, message: 'Orario di inizio e fine sono obbligatori per orari personalizzati' },
          { status: 400 }
        );
      }
    } else {
      if (!orario) {
        return NextResponse.json(
          { success: false, message: 'Orario è obbligatorio' },
          { status: 400 }
        );
      }
    }

    // Validate that the date is a weekday
    if (!isWeekday(dataAppuntamento)) {
      return NextResponse.json(
        { success: false, message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì' },
        { status: 400 }
      );
    }

    // Check if the date is in the past
    const selectedDate = new Date(dataAppuntamento);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json(
        { success: false, message: 'Non è possibile prenotare appuntamenti per date passate' },
        { status: 400 }
      );
    }

    // Get employee ID for availability checking
    let employeeId = null;
    if (operatore && operatore !== 'Qualsiasi') {
      // Try to get employee ID from the operatore field
      const { supabase, TABLES } = await import('../../../../lib/supabase.js');
      try {
        const { data: employees } = await supabase
          .from(TABLES.EMPLOYEES)
          .select('id')
          .eq('name', operatore)
          .eq('is_active', true)
          .limit(1);

        if (employees && employees.length > 0) {
          employeeId = employees[0].id;
        }
      } catch (error) {
        console.warn('Could not find employee ID for:', operatore, error);
      }
    }

    // Check availability based on appointment type
    if (customTimeRange) {
      // For custom time ranges, check using employee-specific logic
      const conflicts = await checkCustomTimeRangeConflicts(dataAppuntamento, startTime, endTime, employeeId);
      if (conflicts.length > 0) {
        const message = employeeId
          ? `L'orario personalizzato selezionato è in conflitto con altri appuntamenti per ${operatore}: ${conflicts.join(', ')}`
          : `L'orario personalizzato selezionato è in conflitto con appuntamenti esistenti: ${conflicts.join(', ')}`;

        return NextResponse.json(
          {
            success: false,
            message,
            code: 'TIME_SLOT_UNAVAILABLE',
            conflicts,
            employeeId
          },
          { status: 409 }
        );
      }
    } else {
      // For predefined slots, check employee-specific availability
      if (!(await isTimeSlotAvailable(dataAppuntamento, orario, employeeId))) {
        const message = employeeId
          ? `L'orario selezionato non è più disponibile per ${operatore}. Seleziona un altro orario o operatore.`
          : 'L\'orario selezionato non è più disponibile. Ricarica la pagina e scegli un altro orario.';

        return NextResponse.json(
          {
            success: false,
            message,
            code: 'TIME_SLOT_UNAVAILABLE',
            employeeId
          },
          { status: 409 } // Conflict status code
        );
      }
    }

    // Create email transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: EMAIL_USER,
        pass: EMAIL_PASS,
      },
    });

    // Email content for user
    const userEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #B42C2C; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">CAF Monte Sacro</h1>
          <h2 style="margin: 10px 0 0 0;">Conferma Appuntamento</h2>
        </div>
        
        <div style="padding: 20px; background-color: #f9f9f9;">
          <h3 style="color: #B42C2C;">Gentile ${nome} ${cognome},</h3>
          <p>Il suo appuntamento è stato confermato con i seguenti dettagli:</p>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p><strong>📅 Data:</strong> ${formatDate(dataAppuntamento)}</p>
            <p><strong>🕐 Orario:</strong> ${orario}</p>
            <p><strong>🏢 Servizio:</strong> ${servizio}</p>
            ${prestazione ? `<p><strong>📋 Prestazione:</strong> ${prestazione}</p>` : ''}
            ${operatore && operatore !== 'Qualsiasi' ? `<p><strong>👤 Operatore:</strong> ${operatore}</p>` : ''}
            <p><strong>📧 Email:</strong> ${email}</p>
            <p><strong>📞 Telefono:</strong> ${telefono}</p>
            ${noteAggiuntive ? `<p><strong>📝 Note:</strong> ${noteAggiuntive}</p>` : ''}
          </div>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p style="margin: 0;"><strong>⚠️ Importante:</strong> L'orario potrebbe subire lievi variazioni. Se al suo arrivo un altro cliente è già in fase di servizio, verrà chiamato subito dopo.</p>
          </div>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h4 style="color: #B42C2C; margin-top: 0;">📍 Come raggiungerci:</h4>
            <p><strong>CAF Monte Sacro</strong><br>
            Via Nomentana, 123<br>
            00141 Roma<br>
            Tel: 06 123456789</p>
          </div>
          
          <p>Grazie per aver scelto i nostri servizi!</p>
          <p style="color: #666; font-size: 12px; margin-top: 20px;">
            Questa è una email automatica, si prega di non rispondere.
          </p>
        </div>
      </div>
    `;

    // Email content for CAF office
    const cafEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #252B59; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Nuovo Appuntamento</h1>
          <h2 style="margin: 10px 0 0 0;">Creato dall'Amministratore</h2>
        </div>

        <div style="padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
            <p style="margin: 0;"><strong>👤 Creato da:</strong> ${user.username} (Admin)</p>
          </div>

          ${!hasValidEmail ? `
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
            <p style="margin: 0;"><strong>⚠️ Attenzione:</strong> Cliente non registrato - nessuna email di conferma inviata al cliente</p>
          </div>
          ` : ''}

          <div style="background-color: white; padding: 15px; border-radius: 5px;">
            <h3 style="color: #252B59; margin-top: 0;">Dettagli Cliente:</h3>
            <p><strong>Nome:</strong> ${nome} ${cognome}</p>
            <p><strong>Email:</strong> ${email}${!hasValidEmail ? ' <em>(Non registrato)</em>' : ''}</p>
            <p><strong>Telefono:</strong> ${telefono}</p>

            <h3 style="color: #252B59;">Dettagli Appuntamento:</h3>
            <p><strong>📅 Data:</strong> ${formatDate(dataAppuntamento)}</p>
            <p><strong>🕐 Orario:</strong> ${orario}</p>
            <p><strong>🏢 Servizio:</strong> ${servizio}</p>
            ${prestazione ? `<p><strong>📋 Prestazione:</strong> ${prestazione}</p>` : ''}
            ${operatore && operatore !== 'Qualsiasi' ? `<p><strong>👤 Operatore richiesto:</strong> ${operatore}</p>` : ''}
            ${noteAggiuntive ? `<p><strong>📝 Note aggiuntive:</strong> ${noteAggiuntive}</p>` : ''}
          </div>
        </div>
      </div>
    `;

    // Save appointment to Supabase
    const savedAppointment = await addAppointment(processedData);

    let emailStatus = { userEmail: false, cafEmail: false };
    let emailErrors = [];

    // Try to send email to user only if email is valid
    if (hasValidEmail) {
      try {
        await transporter.sendMail({
          from: EMAIL_USER,
          to: email,
          subject: 'Conferma Appuntamento CAF',
          html: userEmailContent,
        });
        emailStatus.userEmail = true;
      } catch (emailError) {
        console.error('Error sending user email:', emailError);
        emailErrors.push(`Email utente: ${emailError.message}`);
      }
    } else {
      // Skip sending user email for "Non registrato"
      emailStatus.userEmail = 'skipped';
    }

    // Try to send email to CAF office (always send, but indicate if customer email is missing)
    try {
      const cafSubject = hasValidEmail
        ? `Nuovo Appuntamento (Admin) - ${nome} ${cognome} - ${formatDate(dataAppuntamento)} ${orario}`
        : `Nuovo Appuntamento (Admin - SENZA EMAIL) - ${nome} ${cognome} - ${formatDate(dataAppuntamento)} ${orario}`;

      await transporter.sendMail({
        from: EMAIL_USER,
        to: CAF_EMAIL,
        subject: cafSubject,
        html: cafEmailContent,
      });
      emailStatus.cafEmail = true;
    } catch (emailError) {
      console.error('Error sending CAF email:', emailError);
      emailErrors.push(`Email CAF: ${emailError.message}`);
    }

    // Determine response message based on email success
    let message = 'Appuntamento creato con successo';
    if (emailStatus.userEmail === 'skipped' && emailStatus.cafEmail) {
      message += ' (email cliente non inviata - Non registrato)';
    } else if (emailStatus.userEmail === true && emailStatus.cafEmail) {
      message += ' e email inviate';
    } else if (emailStatus.userEmail === true || emailStatus.cafEmail) {
      message += ', alcune email potrebbero non essere state inviate';
    } else {
      message += ', ma le email non sono state inviate';
    }

    return NextResponse.json({
      success: true,
      message,
      appointment: savedAppointment,
      emailStatus,
      emailErrors: emailErrors.length > 0 ? emailErrors : undefined
    });

  } catch (error) {
    console.error('Error creating admin appointment:', error);

    // Handle specific error types
    if (error.message === 'TIME_SLOT_UNAVAILABLE') {
      return NextResponse.json(
        {
          success: false,
          message: 'L\'orario selezionato non è più disponibile.',
          code: 'TIME_SLOT_UNAVAILABLE'
        },
        { status: 409 }
      );
    }

    // Handle validation errors
    if (error.message.includes('Invalid') || error.message.includes('required')) {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      );
    }

    // Handle database errors
    if (error.message.includes('Database') || error.message.includes('Supabase')) {
      return NextResponse.json(
        { success: false, message: 'Errore del database durante la creazione dell\'appuntamento' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: 'Errore nella creazione dell\'appuntamento',
        error: error.message
      },
      { status: 500 }
    );
  }
}

/**
 * Check for conflicts with existing appointments in the given time range
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {string} startTime - Start time (HH:MM)
 * @param {string} endTime - End time (HH:MM)
 * @returns {Array} Array of conflicting appointment times
 */
async function checkCustomTimeRangeConflicts(date, startTime, endTime) {
  try {
    const { supabase, TABLES, APPOINTMENT_STATUS } = await import('../../../../../lib/supabase.js');

    // Get all appointments for the date
    const { data: appointments, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('orario, start_time, end_time')
      .eq('data_appuntamento', date)
      .neq('status', APPOINTMENT_STATUS.CANCELLED);

    if (error) {
      console.error('Error fetching appointments for conflict check:', error);
      throw new Error(`Failed to check conflicts: ${error.message}`);
    }

    const conflicts = [];
    const requestStart = new Date(`2000-01-01T${startTime}:00`);
    const requestEnd = new Date(`2000-01-01T${endTime}:00`);

    for (const appointment of appointments) {
      let appointmentStart, appointmentEnd;

      // Handle both custom time ranges and predefined slots
      if (appointment.start_time && appointment.end_time) {
        // Custom time range appointment
        appointmentStart = new Date(`2000-01-01T${appointment.start_time}:00`);
        appointmentEnd = new Date(`2000-01-01T${appointment.end_time}:00`);
      } else if (appointment.orario) {
        // Predefined slot appointment (assume 30-minute duration)
        appointmentStart = new Date(`2000-01-01T${appointment.orario}:00`);
        appointmentEnd = new Date(appointmentStart.getTime() + 30 * 60000); // Add 30 minutes
      } else {
        continue; // Skip invalid appointments
      }

      // Check for overlap
      if (requestStart < appointmentEnd && requestEnd > appointmentStart) {
        const conflictTime = appointment.start_time && appointment.end_time
          ? `${appointment.start_time}-${appointment.end_time}`
          : appointment.orario;
        conflicts.push(conflictTime);
      }
    }

    return conflicts;
  } catch (error) {
    console.error('Error in checkCustomTimeRangeConflicts:', error);
    throw error;
  }
}
