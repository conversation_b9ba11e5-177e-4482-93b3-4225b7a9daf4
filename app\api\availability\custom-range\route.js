import { NextResponse } from 'next/server';
import { isTimeSlotAvailable } from '../../../../lib/supabaseAppointmentUtils.js';
import { isWeekday } from '../../../../lib/utils.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const startTime = searchParams.get('startTime');
    const endTime = searchParams.get('endTime');

    if (!date || !startTime || !endTime) {
      return NextResponse.json(
        { success: false, message: 'Data, orario di inizio e orario di fine sono obbligatori' },
        { status: 400 }
      );
    }

    // Validate that the date is a weekday
    if (!isWeekday(date)) {
      return NextResponse.json({
        success: false,
        message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì',
        isAvailable: false
      });
    }

    // Check if the date is in the past
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json({
        success: false,
        message: 'Non è possibile prenotare appuntamenti per date passate',
        isAvailable: false
      });
    }

    // Validate time format (HH:MM)
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(startTime) || !timeRegex.test(endTime)) {
      return NextResponse.json({
        success: false,
        message: 'Formato orario non valido (usa HH:MM)',
        isAvailable: false
      });
    }

    // Validate time range
    const start = new Date(`2000-01-01T${startTime}:00`);
    const end = new Date(`2000-01-01T${endTime}:00`);
    
    if (end <= start) {
      return NextResponse.json({
        success: false,
        message: 'L\'orario di fine deve essere successivo all\'orario di inizio',
        isAvailable: false
      });
    }

    // Check business hours (9 AM - 6 PM)
    const businessStart = new Date(`2000-01-01T09:00:00`);
    const businessEnd = new Date(`2000-01-01T18:00:00`);
    
    if (start < businessStart || end > businessEnd) {
      return NextResponse.json({
        success: false,
        message: 'Gli orari devono essere compresi tra le 09:00 e le 18:00',
        isAvailable: false
      });
    }

    // Check for conflicts with existing appointments
    // We need to check if any existing appointment overlaps with the custom time range
    const conflicts = await checkTimeRangeConflicts(date, startTime, endTime);
    
    if (conflicts.length > 0) {
      return NextResponse.json({
        success: false,
        message: `Conflitto con appuntamenti esistenti: ${conflicts.join(', ')}`,
        isAvailable: false,
        conflicts
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Orario disponibile',
      isAvailable: true,
      date,
      startTime,
      endTime,
      duration: calculateDuration(startTime, endTime)
    });

  } catch (error) {
    console.error('Error checking custom time range availability:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        isAvailable: false
      },
      { status: 500 }
    );
  }
}

/**
 * Check for conflicts with existing appointments in the given time range
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {string} startTime - Start time (HH:MM)
 * @param {string} endTime - End time (HH:MM)
 * @returns {Array} Array of conflicting appointment times
 */
async function checkTimeRangeConflicts(date, startTime, endTime) {
  try {
    // Import here to avoid circular dependencies
    const { supabase, TABLES, APPOINTMENT_STATUS } = await import('../../../../lib/supabase.js');
    
    // Get all appointments for the date
    const { data: appointments, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('orario, start_time, end_time')
      .eq('data_appuntamento', date)
      .neq('status', APPOINTMENT_STATUS.CANCELLED);

    if (error) {
      console.error('Error fetching appointments for conflict check:', error);
      throw new Error(`Failed to check conflicts: ${error.message}`);
    }

    const conflicts = [];
    const requestStart = new Date(`2000-01-01T${startTime}:00`);
    const requestEnd = new Date(`2000-01-01T${endTime}:00`);

    for (const appointment of appointments) {
      let appointmentStart, appointmentEnd;

      // Handle both custom time ranges and predefined slots
      if (appointment.start_time && appointment.end_time) {
        // Custom time range appointment
        appointmentStart = new Date(`2000-01-01T${appointment.start_time}:00`);
        appointmentEnd = new Date(`2000-01-01T${appointment.end_time}:00`);
      } else if (appointment.orario) {
        // Predefined slot appointment (assume 30-minute duration)
        appointmentStart = new Date(`2000-01-01T${appointment.orario}:00`);
        appointmentEnd = new Date(appointmentStart.getTime() + 30 * 60000); // Add 30 minutes
      } else {
        continue; // Skip invalid appointments
      }

      // Check for overlap
      if (requestStart < appointmentEnd && requestEnd > appointmentStart) {
        const conflictTime = appointment.start_time && appointment.end_time 
          ? `${appointment.start_time}-${appointment.end_time}`
          : appointment.orario;
        conflicts.push(conflictTime);
      }
    }

    return conflicts;
  } catch (error) {
    console.error('Error in checkTimeRangeConflicts:', error);
    throw error;
  }
}

/**
 * Calculate duration between two times
 * @param {string} startTime - Start time (HH:MM)
 * @param {string} endTime - End time (HH:MM)
 * @returns {string} Duration in format "X hours Y minutes"
 */
function calculateDuration(startTime, endTime) {
  const start = new Date(`2000-01-01T${startTime}:00`);
  const end = new Date(`2000-01-01T${endTime}:00`);
  const diffMs = end - start;
  const diffMinutes = Math.floor(diffMs / 60000);
  
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;
  
  if (hours > 0 && minutes > 0) {
    return `${hours} ore e ${minutes} minuti`;
  } else if (hours > 0) {
    return `${hours} ore`;
  } else {
    return `${minutes} minuti`;
  }
}
